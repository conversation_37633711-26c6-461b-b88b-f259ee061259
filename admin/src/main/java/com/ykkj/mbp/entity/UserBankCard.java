package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

@Data
public class UserBankCard {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String cardNo;

    private String bankName;

    private String realName;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date createTime;
}
