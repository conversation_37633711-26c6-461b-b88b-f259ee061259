package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * null
 * <AUTHOR>
 * @date 2025/07/11 09:59
 */
@Data
@TableName("user_report")
public class UserReport {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String content;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    private String targetId;

    private String targetType;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

}
