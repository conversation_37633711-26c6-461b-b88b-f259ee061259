package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/25 10:08
 */
@Data
@TableName("activity_price")
public class ActivityPrice {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer activityId;

    private String skuName;

    private Integer sellCount;

    private Integer stock;

    private BigDecimal price;

}
