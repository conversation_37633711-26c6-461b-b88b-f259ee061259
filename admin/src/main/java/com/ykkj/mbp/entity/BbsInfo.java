package com.ykkj.mbp.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/11 15:39
 */
@Data
@TableName("bbs_info")
public class BbsInfo {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private Integer categoryId;

    private String avatar;

    private String userId;

    private String background;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    private String rule;

    private String description;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> groupIds;
}
