package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/06/20 11:28
 */
@Data
@TableName("user_card_comment_reply")
public class UserCardCommentReply {

    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    private String userId;

    private String toUserId;

    private Long commentId;

    private String content;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    private Integer deleted;

}
