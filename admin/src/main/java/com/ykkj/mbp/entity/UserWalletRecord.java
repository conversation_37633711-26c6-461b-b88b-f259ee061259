package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("user_wallet_record")
public class UserWalletRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String userId;
    private String transactionType;
    private BigDecimal amount;
    private String description;
    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;
}
