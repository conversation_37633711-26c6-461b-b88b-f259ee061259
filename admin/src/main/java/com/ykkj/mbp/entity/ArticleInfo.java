package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/21 14:08
 */
@Data
@TableName("article_info")
public class ArticleInfo {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String title;

    private String content;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date createTime;

    private String ip;

}
