package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("group_member_identity")
public class GroupMemberIdentity {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String groupId;
    private String memberId;
    private Long identity;
}
