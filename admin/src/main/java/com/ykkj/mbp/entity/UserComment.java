package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

@Data
@TableName("user_comment")
public class UserComment {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String targetId;

    private Integer baseScore;

    private String content;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    private Integer anonymous = 0;

    private Integer value1 = 0;//默认 感知层
    private Integer value2 = 0;//默认 角色层
    private Integer value3 = 0;//默认 资源层
    private Integer value4 = 0;//默认 能力圈
    private Integer value5 = 0;//默认 内核

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date createTime;

    private Long parentId = 0l;
}
