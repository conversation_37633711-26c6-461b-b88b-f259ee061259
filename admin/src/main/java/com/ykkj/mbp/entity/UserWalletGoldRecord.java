package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * null
 * <AUTHOR>
 * @date 2025/05/24 10:35
 */
@Data
@TableName("user_wallet_gold_record")
public class UserWalletGoldRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String transactionType;

    private Integer gold;

    private String description;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date createTime;

}
