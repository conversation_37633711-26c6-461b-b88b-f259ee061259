package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("bbs_browse")
public class BbsBrowse {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long bbsId;

    private String userId;

    private Date browseTime;
}
