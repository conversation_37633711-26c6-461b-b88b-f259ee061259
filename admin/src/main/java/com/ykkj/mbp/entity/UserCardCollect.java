package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * null
 * <AUTHOR>
 * @date 2025/07/09 17:09
 */
@Data
@TableName("user_card_collect")
public class UserCardCollect {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private Long cardId;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

}
