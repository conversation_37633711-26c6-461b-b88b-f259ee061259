package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/24 10:58
 */
@Data
@TableName("user_info")
public class UserInfo {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String earthId;

    private String mobile;

    private String avatar;

    private Integer gender;

    private String displayName;

    private String city;

    private String signature;

    private java.util.Date birthday;

    private String school;

    private String openId;

    private String unionId;

    private String cover;

    private Integer level = 0;

    private Integer accountStatus = 0;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date createTime;

    @TableField(fill = FieldFill.UPDATE)
    private java.util.Date updateTime;

}
