package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/15 12:29
 */
@Data
@TableName("bbs_comment")
public class BbsComment {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String toUserId;

    private Long floorId;

    private String content;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableLogic(value = "0",delval = "1")
    private Integer deleted = 0;
}
