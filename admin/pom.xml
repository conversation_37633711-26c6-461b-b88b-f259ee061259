<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cl</artifactId>
        <groupId>com.ykkj</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>admin</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.32</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.12</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser-4.9</artifactId>
            <version>3.5.12</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.26</version>
        </dependency>
        <dependency>
            <groupId>cloud.liblibai.openapi</groupId>
            <artifactId>java-sdk</artifactId>
            <version>0.0.9</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join-boot-starter</artifactId>
            <version>1.5.3</version>
        </dependency>
        <!-- 一键登录获取手机号-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dypnsapi20170525</artifactId>
            <version>1.2.3</version>
        </dependency>
        <!--以下野火sdk指定jar包-->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.9</version>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>cn.wildfirechat</groupId>
            <artifactId>sdk</artifactId>
            <version>1.3.6</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/sdk-1.3.6.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>cn.wildfirechat</groupId>
            <artifactId>common</artifactId>
            <version>1.3.6</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/common-1.3.6.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.ykkj</groupId>
            <artifactId>yk-core</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/yk-core-1.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.ykkj</groupId>
            <artifactId>yk-alipay</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/yk-alipay-1.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.ykkj</groupId>
            <artifactId>yk-oss</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/yk-oss-1.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.ykkj</groupId>
            <artifactId>yk-redis</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/yk-redis-1.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.ykkj</groupId>
            <artifactId>yk-sms</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/yk-sms-1.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.ykkj</groupId>
            <artifactId>yk-weixin</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/yk-weixin-1.0.jar</systemPath>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
