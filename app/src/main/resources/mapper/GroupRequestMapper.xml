<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ykkj.mbp.mapper.GroupRequestMapper">

    <select id="auditList" resultType="com.ykkj.mbp.entity.GroupRequest">
        SELECT gr.*
        FROM group_request  gr
        LEFT JOIN group_info gi on gr.gid = gi.gid
        where gr.status = 0 and gi.creator = #{userId}
    </select>
</mapper>