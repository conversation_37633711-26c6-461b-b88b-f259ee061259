<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ykkj.mbp.mapper.BbsTopicMapper">
    <select id="getRecommendTopic" resultMap="topicResultMap">
        SELECT bi.avatar, bi.name,bt.title,bt.content,bt.url,bi.id as bbsId,bt.id as topicId,bm.name as moduleName
        from bbs_topic bt
        left join bbs_info bi on bt.bbs_id = bi.id
        left join bbs_category bc on bi.category_id = bc.id
        left join bbs_module bm on bt.module_id = bm.id
        <where>
            deleted = 0
            <if test="categoryId != null">
                and bc.id = #{categoryId}
            </if>
        </where>
        order by bt.create_time desc
    </select>

    <resultMap id="topicResultMap" type="com.ykkj.mbp.dto.TopicDTO">
        <result column="url" property="url" typeHandler="com.ykkj.mbp.typeHandler.List2StringTypeHandler"/>
    </resultMap>
</mapper>