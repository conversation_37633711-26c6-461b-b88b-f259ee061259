<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ykkj.mbp.mapper.UserInfoMapper">
    <select id="findByUnionId" resultType="com.ykkj.mbp.entity.UserInfo">
        SELECT * from user_info where union_id = #{unionId}
    </select>

    <select id="findByPhone" resultType="com.ykkj.mbp.entity.UserInfo">
        SELECT * from user_info where mobile = #{phone}
    </select>
    
    <select id="searchUserInfo" resultType="com.ykkj.mbp.dto.UserInfoDTO">
        SELECT
            ui.user_id, ui.earth_id, ui.display_name, ui.avatar,
            CASE
                WHEN bur.id IS NOT NULL THEN 1
                ELSE 0
                END AS is_followed
        FROM
            user_info ui
                LEFT JOIN
            bbs_user_relation bur
            ON
                ui.user_id = bur.target_id
                    AND bur.user_id = #{userId}
        WHERE
            ui.display_name LIKE CONCAT('%', #{keyword}, '%')
    </select>
</mapper>