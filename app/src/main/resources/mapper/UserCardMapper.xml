<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ykkj.mbp.mapper.UserCardMapper">
    <select id="getUserCard" resultMap="cardResultMap">
        select uc.*,ui.avatar,ucr.role_name,ui.gender
        from user_card uc
        left join user_card_role ucr on uc.role_id = ucr.id
        left join user_info ui on uc.user_id = ui.user_id
        where uc.user_id = #{userId} and uc.deleted = 0
    </select>

    <select id="getCardById" resultMap="cardResultMap">
        select uc.*,ui.avatar,ucr.role_name,ui.gender
        from user_card uc
                 left join user_card_role ucr on uc.role_id = ucr.id
                 left join user_info ui on uc.user_id = ui.user_id
        where uc.id = #{cardId} and uc.deleted = 0
    </select>

    <resultMap id="cardResultMap" type="com.ykkj.mbp.dto.UserCardDTO">
        <result column="platform_index" property="platformIndex" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>
</mapper>