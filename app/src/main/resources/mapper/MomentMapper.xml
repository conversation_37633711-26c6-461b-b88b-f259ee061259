<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ykkj.mbp.mapper.MomentMapper">
    <select id="getFriendMoment" resultType="com.ykkj.mbp.entity.Moment">
        SELECT m.*
        FROM moment m
        WHERE m.deleted = 0
          AND (
            m.user_id = #{userId}
                OR EXISTS (SELECT 1
                           FROM friend_relation fr
                           WHERE fr.user_id = #{userId}
                             AND fr.friend_uid = m.user_id)
            )
        ORDER BY m.create_time DESC
    </select>

</mapper>