<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ykkj.mbp.mapper.UserCardDisplayGroupMapper">
    <select id="getDisplayList" resultType="com.ykkj.mbp.entity.UserCardDisplayGroup">
        select
            case
                when ucdg.group_type = 1 then gi.name
                when ucdg.group_type = 2 then ucdg.group_name
            end as groupName,
            case
                when ucdg.group_type = 1 then gi.avatar
                when ucdg.group_type = 2 then ucdg.avatar
            end as avatar,ucdg.id,ucdg.group_type
        from user_card_display_group ucdg
        left join group_info gi on ucdg.group_id = gi.gid
        where ucdg.user_id = #{userId}
    </select>

    <select id="getCardDisplayList" resultType="com.ykkj.mbp.entity.UserCardDisplayGroup">
        select
            case
                when ucdg.group_type = 1 then gi.name
                when ucdg.group_type = 2 then ucdg.group_name
                end as groupName,
            case
                when ucdg.group_type = 1 then gi.avatar
                when ucdg.group_type = 2 then ucdg.avatar
                end as avatar,ucdg.id,ucdg.group_type,ucdg.description
        from user_card_display_group ucdg
                 left join group_info gi on ucdg.group_id = gi.gid
        where ucdg.card_id = #{cardId}
    </select>
</mapper>