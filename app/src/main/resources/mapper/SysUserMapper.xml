<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ykkj.mbp.mapper.SysUserMapper">
    <select id="findByUnionId" resultType="com.ykkj.mbp.entity.SysUser">
        SELECT * from sys_user where union_id = #{unionId}
    </select>

    <select id="findByPhone" resultType="com.ykkj.mbp.entity.SysUser">
        SELECT * from sys_user where phone = #{phone}
    </select>
</mapper>