package com.ykkj;

import cn.wildfirechat.sdk.AdminConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
//import org.springframework.data.neo4j.repository.config.EnableNeo4jRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableScheduling
//@EnableNeo4jRepositories("com.ykkj.app.repository")
@EnableTransactionManagement
@SpringBootApplication
public class AppApplication {

    private static String AdminUrl = "http://127.0.0.1:18080";
//    private static String AdminUrl = "http://124.220.218.117:18080";
    private static String AdminSecret = "123456";

    public static void main(String[] args) {
        SpringApplication.run(AppApplication.class, args);
    }

    static {
        AdminConfig.initAdmin(AdminUrl, AdminSecret);
    }
}
