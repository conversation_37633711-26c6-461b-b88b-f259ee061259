package com.ykkj.app.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimpleGroupInfo {
    private String groupId;
    private String avatar;
    private String name;

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof SimpleGroupInfo)) return false;
        SimpleGroupInfo that = (SimpleGroupInfo) o;
        return groupId.equals(that.groupId);
    }

    @Override
    public int hashCode() {
        return groupId.hashCode();
//        return Objects.hashCode(groupId);
    }
}
