//package com.ykkj.app.controller.callback;
//
//import com.wechat.pay.java.service.partnerpayments.jsapi.model.Transaction;
//import com.ykkj.app.service.inf.GroupService;
//import com.ykkj.app.service.inf.WalletOrderService;
//import com.ykkj.app.service.inf.WithdrawOrderService;
//import com.ykkj.core.domain.R;
//import com.ykkj.weixin.model.WithdrawResult;
//import com.ykkj.weixin.service.WxMobileService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.servlet.http.HttpServletRequest;
//
//@Slf4j
//@RestController
//@RequestMapping("/cb/wxpay")
//public class WxpayCallback {
//    @Autowired
//    private WxMobileService mobileService;
//    @Autowired
//    private WalletOrderService walletOrderService;
//    @Autowired
//    private WithdrawOrderService withdrawOrderService;
//    @Autowired
//    private GroupService groupService;
//
//    @PostMapping("/charge")
//    public R payCallback(HttpServletRequest request, @RequestBody String body) {
//        Transaction transaction = mobileService.notifyAndGetResult(request, body);
//        if (Transaction.TradeStateEnum.SUCCESS.equals(transaction.getTradeState())) {
//            Integer totalAmount = transaction.getAmount().getTotal();
//            String transactionId = transaction.getTransactionId();
//            String outTradeNo = transaction.getOutTradeNo();
//
//            log.info("订单支付成功，订单号：" + outTradeNo + "，支付金额：" + totalAmount);
//            walletOrderService.paySuccess(outTradeNo, transactionId);
//        }
//        return R.ok();
//    }
//
//    @PostMapping("/gold")
//    public R payGoldCallback(HttpServletRequest request, @RequestBody String body) {
//        Transaction transaction = mobileService.notifyAndGetResult(request, body);
//        if (Transaction.TradeStateEnum.SUCCESS.equals(transaction.getTradeState())) {
//            Integer totalAmount = transaction.getAmount().getTotal();
//            String transactionId = transaction.getTransactionId();
//            String outTradeNo = transaction.getOutTradeNo();
//
//            log.info("订单支付成功，订单号：" + outTradeNo + "，支付金额：" + totalAmount);
//            walletOrderService.paySuccessGold(outTradeNo, transactionId);
//        }
//        return R.ok();
//    }
//
//    @PostMapping("/group")
//    public R groupPayCallback(HttpServletRequest request, @RequestBody String body) {
//        Transaction transaction = mobileService.notifyAndGetResult(request, body);
//        if (Transaction.TradeStateEnum.SUCCESS.equals(transaction.getTradeState())) {
//            Integer totalAmount = transaction.getAmount().getTotal();
//            String transactionId = transaction.getTransactionId();
//            String outTradeNo = transaction.getOutTradeNo();
//
//            log.info("订单支付成功，订单号：" + outTradeNo + "，支付金额：" + totalAmount);
//            groupService.paySuccess(outTradeNo, transactionId);
//        }
//        return R.ok();
//    }
//
//    @PostMapping("/withdraw")
//    public R withdrawCallback(HttpServletRequest request, @RequestBody String body) {
//        WithdrawResult withdrawResult = mobileService.notifyWithdraw(request, body);
//        System.out.println(withdrawResult);
//        if(withdrawResult == null){
//            return R.fail("提现失败");
//        }
//        if ("SUCCESS".equals(withdrawResult.getState())) {
//            Integer totalAmount = withdrawResult.getTransferAmount();
//            String transactionId = withdrawResult.getTransferBillNo();
//            String outTradeNo = withdrawResult.getOutBillNo();
//
//            log.info("订单提现成功，订单号：" + outTradeNo + "，提现金额：" + totalAmount);
//            withdrawOrderService.withdrawSuccess(outTradeNo, transactionId);
//        }
//        return R.ok();
//    }
//}
