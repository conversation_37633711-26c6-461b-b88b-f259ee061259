package com.ykkj.app.service;

import cn.wildfirechat.pojos.OutputGroupIds;
import cn.wildfirechat.pojos.PojoGroupMember;
import cn.wildfirechat.sdk.GroupAdmin;
import cn.wildfirechat.sdk.model.IMResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ykkj.alipay.model.AlipayParams;
import com.ykkj.alipay.service.AlipayTradeAppPayService;
import com.ykkj.app.avatar.AvatarService;
import com.ykkj.app.avatar.GroupAvatarRequest;
import com.ykkj.app.constant.RedisKey;
import com.ykkj.app.enums.PayType;
import com.ykkj.app.pojo.GroupRequest;
import com.ykkj.app.service.inf.GroupService;
import com.ykkj.app.service.inf.UserWalletRecordService;
import com.ykkj.app.service.inf.UserWalletService;
import com.ykkj.app.service.inf.WalletOrderService;
import com.ykkj.app.vo.GroupInfoVO;
import com.ykkj.app.vo.SimpleUserInfo;
import com.ykkj.core.exception.ServiceException;
import com.ykkj.core.utils.StringUtils;
import com.ykkj.core.utils.uuid.OrderIDGenerator;
import com.ykkj.core.utils.uuid.UUID;
import com.ykkj.mbp.config.MyPage;
import com.ykkj.mbp.entity.Group;
import com.ykkj.mbp.entity.GroupCategory;
import com.ykkj.mbp.entity.UserWallet;
import com.ykkj.mbp.entity.WalletOrder;
import com.ykkj.mbp.mapper.GroupMapper;
import com.ykkj.redis.service.RedisService;
import com.ykkj.security.utils.SecurityUtils;
import com.ykkj.weixin.model.Amount;
import com.ykkj.weixin.model.PayParams;
import com.ykkj.weixin.model.PaySignParams;
import com.ykkj.weixin.service.WxMobileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.geo.GeoResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GroupServiceImpl extends ServiceImpl<GroupMapper, Group> implements GroupService {
    @Autowired
    private AvatarService avatarService;
    @Autowired
    private UserService userService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private WxMobileService mobileService;
    @Autowired
    private AlipayTradeAppPayService aliPayService;
    @Autowired
    private WalletOrderService walletOrderService;
    @Autowired
    private UserWalletService walletService;
    @Value("${weixin.mchid}")
    private String mchId;
    @Autowired
    private UserWalletRecordService userWalletRecordService;

    @Override
    public void add(GroupRequest request) {
        if (StringUtils.isEmpty(request.getGid())) {
            throw new ServiceException("gid不能为空");
        }
        if (StringUtils.isEmpty(request.getName())) {
            throw new ServiceException("name不能为空");
        }
        if (StringUtils.isEmpty(request.getCreator())) {
            throw new ServiceException("creator不能为空");
        }
        try {
            Group group = new Group();
            group.setGid(request.getGid());
            group.setName(request.getName());
            group.setCreator(request.getCreator());
            group.setLongitude(request.getLongitude());
            group.setLatitude(request.getLatitude());
            List<PojoGroupMember> memberList = GroupAdmin.getGroupMembers(request.getGid()).getResult().getMembers();
            List<GroupAvatarRequest.GroupMemberInfo> list = new ArrayList<>();
            memberList.forEach(member -> {
                SimpleUserInfo userInfo = userService.getUserBasicInfo(member.getMember_id());
                GroupAvatarRequest.GroupMemberInfo memberInfo = new GroupAvatarRequest.GroupMemberInfo();
                memberInfo.setName(userInfo.getDisplayName());
                memberInfo.setAvatarUrl(userInfo.getAvatar());
                list.add(memberInfo);
            });
            GroupAvatarRequest groupAvatarRequest = new GroupAvatarRequest();
            groupAvatarRequest.setMembers(list);
            String groupAvatar = avatarService.createGroupAvatar(groupAvatarRequest);
            GroupAdmin.modifyGroupInfo(request.getCreator(), request.getGid(), 1,
                    "https://api.ykjrzl.com/avatar/" + groupAvatar, null, null);
            group.setAvatar("https://api.ykjrzl.com/avatar/" + groupAvatar);
            this.save(group);
            if (request.getLongitude() != null && request.getLatitude() != null) {
                redisService.geoAdd(RedisKey.GROUP_LOCATION, group, request.getLongitude(), request.getLatitude());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void editGroupName(GroupRequest request) {
        if (StringUtils.isEmpty(request.getGid())) {
            throw new ServiceException("gid不能为空");
        }
        if (StringUtils.isEmpty(request.getName())) {
            throw new ServiceException("name不能为空");
        }
        if (checkNameExist(request.getName())) {
            throw new ServiceException("该名称已被使用");
        }

        LambdaUpdateWrapper<Group> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(Group::getName, request.getName());
        wrapper.eq(Group::getGid, request.getGid());
        try {
            this.update(wrapper);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void delGroup(GroupRequest request) {
        LambdaQueryWrapper<Group> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Group::getGid, request.getGid());
        remove(wrapper);
    }

    @Override
    public IPage<GroupInfoVO> searchGroup(IPage page, GroupRequest request) {
        String userId = SecurityUtils.getUserId();
        List<String> groupIds = getUserGroupIds(userId);
        MPJLambdaWrapper<Group> wrapper = new MPJLambdaWrapper<>();
        wrapper.leftJoin(GroupCategory.class, GroupCategory::getId, Group::getCategoryId);
        wrapper.eq(request.getCategoryId() != null, Group::getCategoryId, request.getCategoryId());
//        if (request.getJoined() != null && request.getJoined() == 0) {
//            if (groupIds != null && groupIds.size() == 0) {
//                wrapper.notIn(Group::getGid, groupIds);
//            }
//        } else if (request.getJoined() != null && request.getJoined() == 1) {
//            if (groupIds != null && groupIds.size() == 0) {
//                wrapper.in(Group::getGid, groupIds);
//            }
//        }
        if(groupIds != null && groupIds.size() > 0){
            if (request.getJoined() != null && request.getJoined() == 0){
                wrapper.notIn(Group::getGid, groupIds);
            }else if(request.getJoined() != null && request.getJoined() == 1){
                wrapper.in(Group::getGid, groupIds);
            }
        }

        wrapper.like(StringUtils.isNotEmpty(request.getKeyword()), Group::getName, request.getKeyword());
        wrapper.select(Group::getGid, Group::getName, Group::getDescription, Group::getCreator, Group::getAvatar, Group::getPrice);
        wrapper.selectAs(GroupCategory::getName, "categoryName");
        IPage<GroupInfoVO> iPage = getBaseMapper().selectJoinPage(page, GroupInfoVO.class, wrapper);
        return iPage.convert(o -> {
            if (groupIds.contains(o.getGid())) {
                o.setJoined(1);
            }
            return o;
        });
    }

    public List<String> getUserGroupIds(String userId) {
        try {
            List<String> groupIds = GroupAdmin.getUserGroups(userId).getResult().getGroupIds();
            return groupIds;
        } catch (Exception e) {
            log.error("野火查询用户群信息失败 userId = {}", userId);
            throw new ServiceException("查询群信息失败");
        }
    }

    @Override
    public MyPage<GroupInfoVO> searchGroupAsync(IPage page, GroupRequest request) {
        log.info("查询群 - 线程：" + Thread.currentThread().getName());
        IPage<GroupInfoVO> searchResult = searchGroup(page, request);
        return new MyPage<>(searchResult.getCurrent(), searchResult.getSize(), searchResult.getTotal(), searchResult.getRecords());

    }

    @Override
    public List<GeoResult<Group>> getNearGroup(double longitude, double latitude, double distance) {
        List<GeoResult<Group>> geoResults = redisService.geoSearch(RedisKey.GROUP_LOCATION, longitude, latitude, distance);
        return geoResults;
    }

    @Override
    public void editCategory(GroupRequest request) {
        LambdaUpdateWrapper<Group> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(Group::getCategoryId, request.getCategoryId());
        wrapper.eq(Group::getGid, request.getGid());
        update(wrapper);
    }

    @Override
    public void editAvatar(GroupRequest request) {
        LambdaUpdateWrapper<Group> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(Group::getAvatar, request.getAvatar());
        wrapper.eq(Group::getGid, request.getGid());
        update(wrapper);
    }

    @Override
    public void editDescriotion(GroupRequest request) {
        LambdaUpdateWrapper<Group> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(Group::getDescription, request.getDescription());
        wrapper.eq(Group::getGid, request.getGid());
        update(wrapper);
    }

    public boolean checkNameExist(String name) {
        return this.baseMapper.selectCount(new LambdaUpdateWrapper<Group>().eq(Group::getName, name)) > 0;
    }

    @Override
    public void setPrice(String gid, BigDecimal price, Integer duration) {
        LambdaUpdateWrapper<Group> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(Group::getPrice, price);
        wrapper.set(Group::getDuration, duration);
        wrapper.eq(Group::getGid, gid);
        update(wrapper);
    }


    @Override
    public Object pay(String gid, Integer payType, String pwd) {
        String orderNo = OrderIDGenerator.generateOrderId();
        LambdaQueryWrapper<Group> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Group::getGid, gid);
        Group group = getOne(wrapper);
        if (PayType.ALIPAY.getCode() == payType) {
            walletOrderService.generateOrder(SecurityUtils.getUserId(), orderNo, group.getPrice(), payType, gid);
            AlipayParams params = AlipayParams.builder()
                    .orderNo(orderNo)
                    .description("付费进群")
                    .amount(group.getPrice())
                    .callbackUrl("https://api.ykjrzl.com/cb/alipay/group")
                    .build();
            return aliPayService.pay(params);
        } else if (PayType.WECHAT.getCode() == payType) {
            walletOrderService.generateOrder(SecurityUtils.getUserId(), orderNo, group.getPrice(), payType, gid);
            Amount amount = new Amount(group.getPrice().multiply(new BigDecimal(100)).intValue(), "cny");
            PayParams params = PayParams.builder()
                    .orderNo(orderNo)
                    .description("付费进群")
                    .amount(amount)
                    .callbackUrl("https://api.ykjrzl.com/cb/wxpay/group")
                    .build();
            return getPaySign(mobileService.appPay(params).getPrepayId());
        } else if (PayType.GOLD.getCode() == payType) {
            handleGoldPay(group);
        }
        return null;
    }

    @Transactional
    public void handleGoldPay(Group group) {
        UserWallet wallet = walletService.getByUserId(SecurityUtils.getUserId());
        Integer goldAmount = group.getPrice().intValue() * 10;
        if (wallet.getGold().compareTo(goldAmount) < 0) {
            throw new ServiceException("金币不足");
        }
        PojoGroupMember member = new PojoGroupMember();
        member.setMember_id(SecurityUtils.getUserId());
        try {
            GroupAdmin.addGroupMembers(SecurityUtils.getUserId(), group.getGid(), Arrays.asList(member), null, null, null);
        } catch (Exception e) {
            throw new ServiceException("进群失败:" + e.getMessage());
        }
        wallet.setBalance(wallet.getBalance().subtract(group.getPrice().multiply(new BigDecimal(10))));
        walletService.updateById(wallet);
    }

    @Transactional
    public void handleBalancePay(Group group, String pwd) {
        UserWallet wallet = walletService.getByUserId(SecurityUtils.getUserId());
        if (!SecurityUtils.matchesPassword(pwd, wallet.getPassword())) {
            throw new ServiceException("密码错误");
        }
        if (wallet.getBalance().compareTo(group.getPrice()) < 0) {
            throw new ServiceException("余额不足");
        }
        PojoGroupMember member = new PojoGroupMember();
        member.setMember_id(SecurityUtils.getUserId());
        try {
            GroupAdmin.addGroupMembers(SecurityUtils.getUserId(), group.getGid(), Arrays.asList(member), null, null, null);
        } catch (Exception e) {
            throw new ServiceException("进群失败:" + e.getMessage());
        }
        wallet.setBalance(wallet.getBalance().subtract(group.getPrice()));
        walletService.updateById(wallet);
        userWalletRecordService.addRecord(SecurityUtils.getUserId(), group.getPrice(), "out", "付费进群");
    }

    @Override
    public Group getByGroupId(String groupId) {
        LambdaQueryWrapper<Group> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Group::getGid, groupId);
        return getOne(wrapper);
    }

    @Override
    @Transactional
    public void paySuccess(String orderNo, String tradeNo) {
        WalletOrder walletOrder = walletOrderService.getByOrderNo(orderNo);
        if (walletOrder == null) {
            log.error("orderNo {} is not exist!!", orderNo);
            return;
        }
        if (walletOrder.getState() == 1) {
            return;
        }
        walletOrder.setState(1);
        walletOrder.setChannelOrderId(tradeNo);
        walletOrderService.updateById(walletOrder);
        try {
            PojoGroupMember member = new PojoGroupMember();
            member.setMember_id(walletOrder.getUserId());
            GroupAdmin.addGroupMembers(walletOrder.getUserId(), walletOrder.getGroupId(), Arrays.asList(member), null, null, null);
        } catch (Exception e) {
            log.error("add group member error", e.getMessage());
        }
    }

    @Override
    public List<GroupInfoVO> createdGroup() {
        try {
            List<String> groupIds = GroupAdmin.getUserGroupsByType(SecurityUtils.getUserId(), Arrays.asList(2)).getResult().getGroupIds();
            return groupIds.stream().map(groupId -> {
                GroupInfoVO vo = new GroupInfoVO();
                MPJLambdaWrapper<Group> wrapper = new MPJLambdaWrapper<>();
                wrapper.leftJoin(GroupCategory.class, GroupCategory::getId, Group::getCategoryId);
                wrapper.select(Group::getGid, Group::getName, Group::getDescription, Group::getCreator, Group::getAvatar, Group::getPrice);
                wrapper.selectAs(GroupCategory::getName, "categoryName");
                wrapper.eq(Group::getGid, groupId);
                return getBaseMapper().selectJoinOne(GroupInfoVO.class, wrapper);
            }).collect(Collectors.toList());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<GroupInfoVO> managedGroup() {
        try {
            List<String> groupIds = GroupAdmin.getUserGroupsByType(SecurityUtils.getUserId(), Arrays.asList(1)).getResult().getGroupIds();
            return groupIds.stream().map(groupId -> {
                GroupInfoVO vo = new GroupInfoVO();
                MPJLambdaWrapper<Group> wrapper = new MPJLambdaWrapper<>();
                wrapper.leftJoin(GroupCategory.class, GroupCategory::getId, Group::getCategoryId);
                wrapper.select(Group::getGid, Group::getName, Group::getDescription, Group::getCreator, Group::getAvatar, Group::getPrice);
                wrapper.selectAs(GroupCategory::getName, "categoryName");
                wrapper.eq(Group::getGid, groupId);
                return getBaseMapper().selectJoinOne(GroupInfoVO.class, wrapper);
            }).collect(Collectors.toList());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void setAudit(String gid, Integer audit) {
        LambdaUpdateWrapper<Group> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Group::getGid, gid);
        wrapper.set(Group::getAudit, audit);
        update(wrapper);
    }

    public PaySignParams getPaySign(String prepayId) {
        PaySignParams signParams = PaySignParams.builder()
                .nonceStr(UUID.randomUUID().toString().replace("-", ""))
                .timestamp(System.currentTimeMillis() / 1000)
                .prepayId(prepayId)
                .mchId(mchId)
                .build();
        String sign = mobileService.getSign(signParams);
        signParams.setSign(sign);
        return signParams;
    }
}
