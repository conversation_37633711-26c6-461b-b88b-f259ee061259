package com.ykkj.app.service;

import cn.wildfirechat.pojos.Conversation;
import cn.wildfirechat.pojos.MessagePayload;
import cn.wildfirechat.sdk.MessageAdmin;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykkj.app.pojo.wildfire.RelationEventRequest;
import com.ykkj.app.service.inf.FriendRelationService;
import com.ykkj.core.exception.ServiceException;
import com.ykkj.mbp.entity.FriendRelation;
import com.ykkj.mbp.mapper.FriendRelationMapper;
import java.lang.invoke.SerializedLambda;

import com.ykkj.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class FriendRelationServiceImpl extends ServiceImpl<FriendRelationMapper, FriendRelation> implements FriendRelationService {

    @Autowired
    private RedisService redisLockFactory;

    private final String tip = "交友请谨慎，防范网络诈骗，包括不限于以下形式:投资、博彩、兼职刷单、模特、卖*、约*、谈感情、冒充客服、冒充公检法等方式骗取钱财。请您注意，请您坚决拒绝金钱来往!!!";

    public void handleRelationEvent(RelationEventRequest request) {
        String lockKey = "lock:RelationEvent:" + request.getUserId() + "-" + request.getTargetId();
        long expireTime = 30000L;
        long waitTime = 5000L;
        process(request);
//        RedisDistributedLock lock = this.redisLockFactory.get(lockKey, expireTime);
//        try {
//            if (lock.tryLock(waitTime)) {
//                process(request);
//            } else {
//                throw new RuntimeException("获取锁失败请稍后重试");
//            }
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//            throw new RuntimeException("获取锁被中断", e);
//        } finally {
//            lock.unlock();
//        }
    }

    private void process(RelationEventRequest request) {
        log.info("开始处理好友关系变更事件 - 线程:" + Thread.currentThread().getName());
        if (request.getType() == 0) {
            if ("0".equals(request.getValue())) {
                removeFriend(request.getUserId(), request.getTargetId());
            } else if ("1".equals(request.getValue())) {
                becomeFriend(request.getUserId(), request.getTargetId());
                sendRecommendMsg(request.getUserId(), request.getTargetId());
                sendRecommendMsg(request.getTargetId(), request.getUserId());
            }
        } else if (request.getType() == 1) {
            LambdaUpdateWrapper<FriendRelation> wrapper = Wrappers.lambdaUpdate();
            wrapper.eq(FriendRelation::getUserId, request.getUserId());
            wrapper.eq(FriendRelation::getFriendUid, request.getTargetId());
            wrapper.set(FriendRelation::getAlias, request.getValue());
            update(wrapper);
        } else if (request.getType() == 2) {
            LambdaUpdateWrapper<FriendRelation> wrapper = Wrappers.lambdaUpdate();
            wrapper.eq(FriendRelation::getUserId, request.getUserId());
            wrapper.eq(FriendRelation::getFriendUid, request.getTargetId());
            wrapper.set(FriendRelation::getBlacked, request.getValue());
            update(wrapper);
        }
        log.info("好友关系变更事件执行完毕 - 线程:" + Thread.currentThread().getName());
    }

    private void sendRecommendMsg(String userId, String targetId) {
        Conversation conversation = new Conversation();
        conversation.setTarget(targetId);
        conversation.setType(0);
        MessagePayload payload = new MessagePayload();
        payload.setType(1100);
        payload.setContent(tip);
        try {
            MessageAdmin.sendMessage(userId, conversation, payload);
        } catch (Exception e) {
            log.error("发送提示消息失败:{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
    }

    private void becomeFriend(String userId, String targetId) {
        LambdaQueryWrapper<FriendRelation> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FriendRelation::getUserId, userId);
        wrapper.eq(FriendRelation::getFriendUid, targetId);
        if (count(wrapper) == 0L) {
            FriendRelation friendRelation = new FriendRelation();
            friendRelation.setUserId(userId);
            friendRelation.setFriendUid(targetId);
            friendRelation.setBlacked(Integer.valueOf(0));
            log.info("开始保存好友关系 {}", friendRelation);
            save(friendRelation);
        }
        LambdaQueryWrapper<FriendRelation> wrapper1 = Wrappers.lambdaQuery();
        wrapper1.eq(FriendRelation::getUserId, targetId);
        wrapper1.eq(FriendRelation::getFriendUid, userId);
        if (count(wrapper1) == 0L) {
            FriendRelation friendRelation1 = new FriendRelation();
            friendRelation1.setUserId(targetId);
            friendRelation1.setFriendUid(userId);
            friendRelation1.setBlacked(Integer.valueOf(0));
            log.info("开始保存好友关系{}", friendRelation1);
            save(friendRelation1);
        }
    }

    private void removeFriend(String userId, String targetId) {
        LambdaQueryWrapper<FriendRelation> wrapper = Wrappers.lambdaQuery();
        wrapper.or(i -> i.and(j -> j.eq(FriendRelation::getUserId, userId).eq(FriendRelation::getFriendUid, targetId))
                .or(j -> j.eq(FriendRelation::getUserId, targetId).eq(FriendRelation::getFriendUid, userId)));
        remove(wrapper);
    }
}
