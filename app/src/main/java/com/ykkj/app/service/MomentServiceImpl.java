package com.ykkj.app.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykkj.app.constant.RedisKey;
import com.ykkj.app.netty.MessageType;
import com.ykkj.app.netty.WebSocketMessage;
import com.ykkj.app.pojo.PublishMomentRequest;
import com.ykkj.app.pojo.TopicPublishRequest;
import com.ykkj.app.service.inf.*;
import com.ykkj.app.vo.*;
import com.ykkj.core.text.Convert;
import com.ykkj.core.utils.StringUtils;
import com.ykkj.mbp.config.MyPage;
import com.ykkj.mbp.dto.TopicDTO;
import com.ykkj.mbp.entity.BbsTopic;
import com.ykkj.mbp.entity.Moment;
import com.ykkj.mbp.mapper.BbsTopicMapper;
import com.ykkj.mbp.mapper.MomentMapper;
import com.ykkj.redis.service.RedisService;
import com.ykkj.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static com.ykkj.app.netty.NettyServerHandler.sendMessage;

/**
 * <AUTHOR>
 * @date 2024/10/14 14:09
 */
@Service
public class MomentServiceImpl extends ServiceImpl<MomentMapper, Moment> implements MomentService {
    @Autowired
    private UserService userService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private MomentCommentService commentService;
    @Autowired
    private MomentService momentService;

    @Override
    public void publishMoment(PublishMomentRequest request) {
        Moment moment = new Moment();
        BeanUtil.copyProperties(request, moment);
        moment.setUserId(SecurityUtils.getUserId());
        save(moment);
    }


    @Override
    public IPage<MomentVO> getPage(IPage page, String userId) {
//        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(StringUtils.isNotEmpty(userId), Moment::getUserId, userId);
//        wrapper.orderByDesc(Moment::getCreateTime);
//        IPage<Moment> pageData = page(page, wrapper);
        IPage<Moment> pageData = getBaseMapper().getFriendMoment(page,SecurityUtils.getUserId());
        return pageData.convert(o -> {
            MomentVO vo = new MomentVO();
            BeanUtil.copyProperties(o, vo);
            vo.setUserInfo(userService.getUserBasicInfo(o.getUserId()));
            Set<Object> likeUserIds = redisService.sMembers(RedisKey.USER_LIKE_MOMENT_LIST + ":" + o.getId());
            vo.setLikeUser(getLikeUser(likeUserIds));
            IPage commontPage = new Page(1, 5);
            vo.setComments(MyPage.getResult(commontPage, commentService.getPage(commontPage, o.getId()).getRecords()));
            return vo;
        });
    }

    @Override
    public IPage<MomentVO> getVisionPage(IPage page, String userId) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(userId), Moment::getUserId, userId);
        wrapper.isNotNull(Moment::getUrl);
        wrapper.orderByDesc(Moment::getCreateTime);
        IPage<Moment> pageData = page(page, wrapper);
        return pageData.convert(o -> {
            MomentVO vo = new MomentVO();
            BeanUtil.copyProperties(o, vo);
            vo.setUserInfo(userService.getUserBasicInfo(o.getUserId()));
            Set<Object> likeUserIds = redisService.sMembers(RedisKey.USER_LIKE_MOMENT_LIST + ":" + o.getId());
            vo.setLikeUser(getLikeUser(likeUserIds));
            IPage commontPage = new Page(1, 5);
            vo.setComments(MyPage.getResult(commontPage, commentService.getPage(commontPage, o.getId()).getRecords()));
            return vo;
        });
    }

    public List<SimpleUserInfo> getLikeUser(Set userIds) {
        List<SimpleUserInfo> userInfos = new ArrayList<>();
        for (Object userId : userIds) {
            SimpleUserInfo userInfo = userService.getUserBasicInfo(userId.toString());
            if (userInfo != null) {
                userInfos.add(userInfo);
            }
        }
        return userInfos;
    }

    @Override
    public void like(String userId, Long momentId) {
        boolean isLike = Convert.toBool(
                redisService.sIsMember(RedisKey.USER_LIKE_MOMENT_LIST + ":" + momentId, userId), false
        );
        if (isLike) {
            doCancelLike(userId, momentId);
        } else {
            doLike(userId, momentId);
        }

    }

    @Override
    public List<String> getThreePicMoment(String userId) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getUserId, userId);
        wrapper.isNotNull(Moment::getUrl);
        wrapper.orderByDesc(Moment::getCreateTime);
        wrapper.last("limit 3");
        List<Moment> list = list(wrapper);
        List<String> result = new ArrayList<>();
        for (Moment moment : list) {
            if (result.size() >= 3) {
                break;
            }
            result.addAll(moment.getUrl());
        }
        return result.size() > 3 ? result.subList(0, 3) : result;
    }

    @Override
    public IPage<MomentVO> getUserPage(IPage page, String userId) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(userId), Moment::getUserId, userId);
        wrapper.orderByDesc(Moment::getCreateTime);
        IPage<Moment> pageData = page(page, wrapper);
        return pageData.convert(o -> {
            MomentVO vo = new MomentVO();
            BeanUtil.copyProperties(o, vo);
            vo.setUserInfo(userService.getUserBasicInfo(o.getUserId()));
            Set<Object> likeUserIds = redisService.sMembers(RedisKey.USER_LIKE_MOMENT_LIST + ":" + o.getId());
            vo.setLikeUser(getLikeUser(likeUserIds));
            IPage commontPage = new Page(1, 5);
            vo.setComments(MyPage.getResult(commontPage, commentService.getPage(commontPage, o.getId()).getRecords()));
            return vo;
        });
    }

    @Override
    public MomentVO getDetail(Long momentId) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getId, momentId);
        Moment moment = getOne(wrapper);
        MomentVO vo = new MomentVO();
        BeanUtil.copyProperties(moment, vo);
        vo.setUserInfo(userService.getUserBasicInfo(moment.getUserId()));
        Set<Object> likeUserIds = redisService.sMembers(RedisKey.USER_LIKE_MOMENT_LIST + ":" + moment.getId());
        vo.setLikeUser(getLikeUser(likeUserIds));
        return vo;
    }

    private void doLike(String userId, Long momentId) {
        redisService.sAdd(RedisKey.USER_LIKE_MOMENT_LIST + ":" + momentId, userId);
        Moment moment = momentService.getById(momentId);
        if (!moment.getUserId().equals(userId)) {//不给自己发
            NoticeVO notice = new NoticeVO();
            notice.setFromUserId(userId);
            notice.setType("like");
            notice.setMomentId(momentId);
            redisService.lPush(RedisKey.NOTICE_LIST + ":" + moment.getUserId(), notice);
            sendMessage(moment.getUserId(), new WebSocketMessage<>(MessageType.MOMENT_NOTICE, notice));
        }
    }

    private void doCancelLike(String userId, Long momentId) {
        redisService.sRemove(RedisKey.USER_LIKE_MOMENT_LIST + ":" + momentId, userId);
    }

}
