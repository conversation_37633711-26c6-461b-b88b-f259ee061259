package com.ykkj.app.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ykkj.app.constant.RedisKey;
import com.ykkj.app.pojo.UserCommentRequest;
import com.ykkj.app.pojo.UserSubCommentRequest;
import com.ykkj.app.service.inf.UserCommentService;
import com.ykkj.app.service.inf.UserInfoService;
import com.ykkj.app.vo.UserCommentVO;
import com.ykkj.app.vo.UserSubCommentVO;
import com.ykkj.core.exception.ServiceException;
import com.ykkj.core.text.Convert;
import com.ykkj.mbp.entity.UserComment;
import com.ykkj.mbp.mapper.UserCommentMapper;
import com.ykkj.redis.service.RedisService;
import com.ykkj.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

@Service
public class UserCommentServiceImpl extends ServiceImpl<UserCommentMapper, UserComment> implements UserCommentService {
    @Autowired
    private UserService userService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private UserInfoService userInfoService;

    @Override
    @Transactional
    public void publishComment(UserCommentRequest request) {
        int count = Convert.toInt(redisService.hGet(RedisKey.USER_COMMENT, SecurityUtils.getUserId() + "-" + request.getTargetId()), 0);
        if (count >= 3){
            throw new ServiceException("您今天已经点评他三次了，请明天再试");
        }
        UserComment comment = new UserComment();
        BeanUtil.copyProperties(request, comment);
        comment.setUserId(SecurityUtils.getUserId());
        save(comment);
        redisService.hIncr(RedisKey.USER_COMMENT,SecurityUtils.getUserId() + "-" + request.getTargetId(), 1L);

        LambdaQueryWrapper<UserComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserComment::getTargetId, request.getTargetId());
        List<UserComment> list = list(wrapper);
        double v1 = list.stream().mapToDouble(UserComment::getValue1).average().orElse(0.0);
        double v2 = list.stream().mapToDouble(UserComment::getValue2).average().orElse(0.0);
        double v3 = list.stream().mapToDouble(UserComment::getValue3).average().orElse(0.0);

        redisService.hSet(RedisKey.USER_COMMENT_SCORE, request.getTargetId(), Math.round((v1 + v2 + v3) / 3.0 * 10) / 10.0);
        userInfoService.updateLevel(request.getTargetId(), request.getBaseScore());

        redisService.lPush(RedisKey.USER_COMMENT_NOTICE_LIST + ":" + request.getTargetId(), toVO(comment));
    }

    @Override
    public IPage<UserCommentVO> getUserComment(Integer score, String userId, IPage page) {
        LambdaQueryWrapper<UserComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(score != null, UserComment::getBaseScore, score);
        wrapper.eq(UserComment::getTargetId, userId);
        wrapper.orderByDesc(UserComment::getCreateTime);
        wrapper.eq(UserComment::getParentId, 0);
        IPage<UserComment> pageResult = page(page, wrapper);
        return pageResult.convert(comment -> {
            UserCommentVO vo = new UserCommentVO();
            vo.setUrl(comment.getUrl());
            vo.setId(comment.getId());
            vo.setContent(comment.getContent());
            vo.setCreateTime(comment.getCreateTime());
            vo.setUser(comment.getAnonymous() == 1 ? userService.getAnonymousUser() :
                    userService.getUserBasicInfo(comment.getUserId()));
            vo.setLikeNum(Convert.toInt(redisService.hGet(RedisKey.USER_LIKE_USERCOMMENT_COUNT, comment.getId().toString()), 0));
            vo.setSubCommentNum(Convert.toInt(countByParentId(comment.getId()), 0));
            vo.setHasLike(redisService.sIsMember(RedisKey.USER_LIKE_USERCOMMENT_LIST + ":" + comment.getId(), SecurityUtils.getUserId()));
            vo.setSubComment(getFirst(comment.getId()));
//            vo.setScore(Convert.toDouble(redisService.hGet(RedisKey.USER_COMMENT_SCORE, comment.getTargetId()), 0.0));
            vo.setScore(comment.getBaseScore());
            return vo;
        });
    }

    private UserSubCommentVO getFirst(Long id) {
        LambdaQueryWrapper<UserComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserComment::getParentId, id);
        wrapper.last("limit 1");
        UserComment one = getOne(wrapper);
        if (one != null) {
            UserSubCommentVO vo = new UserSubCommentVO();
            BeanUtil.copyProperties(one, vo);
            vo.setUser(userService.getUserBasicInfo(one.getUserId()));
            return vo;
        }
        return null;
    }

    public UserCommentVO toVO(UserComment comment) {
        UserCommentVO vo = new UserCommentVO();
        vo.setUrl(comment.getUrl());
        vo.setId(comment.getId());
        vo.setContent(comment.getContent());
        vo.setCreateTime(comment.getCreateTime());
        vo.setUser(comment.getAnonymous() == 1 ? userService.getAnonymousUser() :
                userService.getUserBasicInfo(comment.getUserId()));
        vo.setScore(comment.getBaseScore());
        return vo;
    }

    private long countByParentId(Long id) {
        LambdaQueryWrapper<UserComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserComment::getParentId, id);
        return count(wrapper);
    }

    @Override
    public void publishSubComment(UserSubCommentRequest request) {
        UserComment comment = new UserComment();
        BeanUtil.copyProperties(request, comment);
        comment.setUserId(SecurityUtils.getUserId());
        save(comment);
    }

    @Override
    public void like(String userId, Long commentId) {
        boolean isLike = Convert.toBool(
                redisService.sIsMember(RedisKey.USER_LIKE_USERCOMMENT_LIST + ":" + commentId, userId), false
        );
        if (isLike) {
            doCancelLike(userId, commentId);
        } else {
            doLike(userId, commentId);
        }
    }

    @Override
    public boolean checkHasComment(String userId, String targetId) {
        LambdaQueryWrapper<UserComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserComment::getUserId, userId);
        wrapper.eq(UserComment::getTargetId, targetId);
        return count(wrapper) > 0;
    }

    @Override
    public IPage<UserSubCommentVO> getSubComment(Long commentId, IPage page) {
        LambdaQueryWrapper<UserComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserComment::getParentId, commentId);
        wrapper.orderByAsc(UserComment::getCreateTime);
        IPage<UserComment> pageResult = page(page, wrapper);
        return pageResult.convert(comment -> {
            UserSubCommentVO vo = new UserSubCommentVO();
            BeanUtil.copyProperties(comment, vo);
            vo.setUser(userService.getUserBasicInfo(comment.getUserId()));
            vo.setHasLike(redisService.sIsMember(RedisKey.USER_LIKE_USERCOMMENT_LIST + ":" + comment.getId(), SecurityUtils.getUserId()));
            vo.setLikeNum(Convert.toInt(redisService.hGet(RedisKey.USER_LIKE_USERCOMMENT_COUNT, comment.getId().toString()), 0));
            return vo;
        });
    }

    @Override
    public List<UserCommentVO> getNotice() {
        List<UserCommentVO> list = redisService.mGet(Collections.singleton(RedisKey.USER_COMMENT_NOTICE_LIST + ":" + SecurityUtils.getUserId()));
        return list;
    }

    private void doLike(String userId, Long commentId) {
        redisService.sAdd(RedisKey.USER_LIKE_USERCOMMENT_LIST + ":" + commentId, userId);
        redisService.hIncr(RedisKey.USER_LIKE_USERCOMMENT_COUNT, commentId.toString(), 1l);
    }

    private void doCancelLike(String userId, Long commentId) {
        redisService.sRemove(RedisKey.USER_LIKE_USERCOMMENT_LIST + ":" + commentId, userId);
        redisService.hDecr(RedisKey.USER_LIKE_USERCOMMENT_COUNT, commentId.toString(), 1l);
    }
}
