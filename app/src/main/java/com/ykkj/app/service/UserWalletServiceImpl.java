//package com.ykkj.app.service;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpResponse;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.alipay.api.domain.Participant;
//import com.alipay.api.response.AlipayFundTransUniTransferResponse;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.ykkj.alipay.model.AlipayParams;
//import com.ykkj.alipay.model.TransferRequest;
//import com.ykkj.alipay.service.AlipayTradeAppPayService;
//import com.ykkj.alipay.service.AlipayTransferService;
//import com.ykkj.app.enums.PayType;
//import com.ykkj.app.service.inf.*;
//import com.ykkj.app.vo.WalletVO;
//import com.ykkj.core.exception.ServiceException;
//import com.ykkj.core.utils.StringUtils;
//import com.ykkj.core.utils.uuid.OrderIDGenerator;
//import com.ykkj.core.utils.uuid.UUID;
//import com.ykkj.mbp.entity.UserWallet;
//import com.ykkj.mbp.mapper.UserWalletMapper;
//import com.ykkj.security.utils.SecurityUtils;
//import com.ykkj.weixin.config.WeixinProperty;
//import com.ykkj.weixin.model.Amount;
//import com.ykkj.weixin.model.PayParams;
//import com.ykkj.weixin.model.PaySignParams;
//import com.ykkj.weixin.service.WxMobileService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.math.BigDecimal;
//import java.util.HashMap;
//import java.util.Map;
//
//@Slf4j
//@Service
//public class UserWalletServiceImpl extends ServiceImpl<UserWalletMapper, UserWallet> implements UserWalletService {
//    @Autowired
//    private AlipayTradeAppPayService aliPayService;
//    @Autowired
//    private AlipayTransferService alipayTransferService;
//    @Autowired
//    private WxMobileService mobileService;
//    @Autowired
//    private UserWalletService walletService;
//    @Autowired
//    private UserWalletRecordService walletRecordService;
//    @Autowired
//    private UserWalletGoldRecordService walletGoldRecordService;
//
//    @Autowired
//    private WalletOrderService walletOrderService;
//    @Value("${weixin.mchid}")
//    private String mchId;
//
//    @Override
//    public WalletVO getUserWallet(String userId) {
//        LambdaQueryWrapper<UserWallet> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(UserWallet::getUserId, userId);
//        UserWallet wallet = getOne(wrapper);
//        if (wallet == null) {
//            createWallet(userId);
//        }
//        WalletVO vo = new WalletVO();
//        BeanUtil.copyProperties(wallet, vo);
//        return vo;
//    }
//
//    @Override
//    public void createWallet(String userId) {
//        LambdaQueryWrapper<UserWallet> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(UserWallet::getUserId, userId);
//        if (count(wrapper) > 0) {
//            throw new ServiceException("已开通");
//        }
//        UserWallet wallet = new UserWallet();
//        wallet.setUserId(userId);
//        wallet.setBalance(BigDecimal.ZERO);
//        wallet.setGold(0);
//        save(wallet);
//    }
//
//    @Override
//    public Object charge(BigDecimal amount, Integer payType) {
//        String orderNo = OrderIDGenerator.generateOrderId();
//        walletOrderService.generateOrder(SecurityUtils.getUserId(), orderNo, amount, payType);
//        if (PayType.ALIPAY.getCode() == payType) {
//            AlipayParams params = AlipayParams.builder()
//                    .orderNo(orderNo)
//                    .description("钱包充值")
//                    .amount(amount)
//                    .callbackUrl("https://api.ykjrzl.com/cb/alipay/charge")
//                    .build();
//            return aliPayService.pay(params);
//        } else if (PayType.WECHAT.getCode() == payType) {
//            Amount amountParam = new Amount(amount.multiply(new BigDecimal(100)).intValue(), "cny");
//            PayParams params = PayParams.builder()
//                    .orderNo(orderNo)
//                    .description("钱包充值")
//                    .amount(amountParam)
//                    .callbackUrl("https://api.ykjrzl.com/cb/wxpay/charge")
//                    .build();
//            return getPaySign(mobileService.appPay(params).getPrepayId());
//        }
//        return null;
//    }
//
//
//    @Override
//    public void increment(String userId, BigDecimal amount) {
//        UserWallet wallet = getByUserId(userId);
//        if (wallet == null) {
//            wallet = new UserWallet();
//            wallet.setUserId(userId);
//            wallet.setBalance(amount);
//            wallet.setGold(0);
//            save(wallet);
//        }
//        wallet.setBalance(wallet.getBalance().add(amount));
//        updateById(wallet);
//    }
//
//    @Override
//    public void decrement(String userId, BigDecimal amount) {
//        UserWallet wallet = getByUserId(userId);
//        if (wallet == null) {
//            log.error("钱包不存在 拒绝提现 userId = {}", userId);
//            throw new ServiceException("非法请求");
//        }
//        wallet.setBalance(wallet.getBalance().subtract(amount));
//        updateById(wallet);
//    }
//
//    @Override
//    public void incrementGold(String userId, BigDecimal amount) {
//        UserWallet wallet = getByUserId(userId);
//        if (wallet == null) {
//            wallet = new UserWallet();
//            wallet.setUserId(userId);
//            wallet.setGold(amount.multiply(new BigDecimal(10)).intValue());
//            wallet.setBalance(BigDecimal.ZERO);
//            save(wallet);
//            return;
//        }
//        wallet.setGold(wallet.getGold() + amount.multiply(new BigDecimal(10)).intValue());
//        updateById(wallet);
//    }
//
//    @Override
//    public void changePwd(String userId, String pwd) {
//        LambdaUpdateWrapper<UserWallet> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.eq(UserWallet::getUserId, userId);
//        wrapper.set(UserWallet::getPassword, SecurityUtils.encryptPassword(pwd));
//        update(wrapper);
//    }
//
//    @Override
//    public void bindWx(String userId, String openId) {
//        UserWallet wallet = getByUserId(userId);
//        wallet.setOpenId(openId);
//        updateById(wallet);
//    }
//
//    @Override
//    public String withdraw(BigDecimal amount, Integer channel) {
//        if(amount.stripTrailingZeros().scale() > 2){
//            throw new ServiceException("提现金额最多两位小数 当前输入:" + amount);
//        }
//        UserWallet wallet = getByUserId(SecurityUtils.getUserId());
//        if (wallet.getBalance().compareTo(amount) < 0){
//            throw new ServiceException("余额不足");
//        }
//        if(PayType.WECHAT.getCode() == channel){
//            if(StringUtils.isEmpty(wallet.getOpenId())){
//                throw new ServiceException("请先绑定微信");
//            }
//            return this.handleWxWithdraw(wallet.getUserId(), wallet.getOpenId(), amount);
//        }else if (PayType.ALIPAY.getCode() == channel){
//            if(StringUtils.isEmpty(wallet.getAlipayAccount()) || StringUtils.isEmpty(wallet.getRealname())){
//                throw new ServiceException("请先绑定支付宝");
//            }
//            return this.handleAlipayWithdraw(wallet.getAlipayAccount(), wallet.getRealname(), amount);
//        }
//        return "";
//    }
//
//    @Override
//    public void bindAlipay(String userId, String alipayAccount, String realname) {
//        LambdaUpdateWrapper<UserWallet> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.eq(UserWallet::getUserId, userId);
//        wrapper.set(UserWallet::getAlipayAccount, alipayAccount);
//        wrapper.set(UserWallet::getRealname, realname);
//        update(wrapper);
//    }
//
//    @Transactional
//    public String handleAlipayWithdraw(String alipayAccount, String realname, BigDecimal amount) {
//        TransferRequest request = new TransferRequest();
//        request.setTitle("地球岛提现");
//        request.setAmount(amount.toString());
//        request.setRemark("地球岛提现");
//        String outTradeNo = OrderIDGenerator.generateOrderId();
//        request.setOutBizNo(outTradeNo);
//        Participant payeeInfo = new Participant();
//        payeeInfo.setIdentity(alipayAccount);
//        payeeInfo.setIdentityType("ALIPAY_LOGON_ID");
//        payeeInfo.setName(realname);
//        request.setParticipant(payeeInfo);
//        try {
//            AlipayFundTransUniTransferResponse response = alipayTransferService.transfer(request);
//            if(response.isSuccess() && "SUCCESS".equals(response.getStatus())){
//                log.info("订单提现成功，订单号：" + outTradeNo + "，提现金额：" + amount);
//                withdrawOrderService.alipayWithdrawSuccess(outTradeNo, response.getOrderId(), amount);
//            }else {
//                log.error("订单提现失败，订单号：" + outTradeNo + "，提现金额：" + amount);
//                withdrawOrderService.aliPayWithdrawFail(outTradeNo, response.getSubMsg(), amount, response.getSubMsg());
//            }
//        } catch (Exception e) {
//            throw new ServiceException("支付宝提现失败");
//        }
//        return outTradeNo;
//    }
//
//    @Autowired
//    private WeixinProperty weixinProperty;
//    @Autowired
//    private WithdrawOrderService withdrawOrderService;
//
//    public String handleWxWithdraw(String userId, String openId, BigDecimal amount) {
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("appid", weixinProperty.getMobile().getAppid());
//        String orderNo = OrderIDGenerator.generateOrderId();
//        jsonObject.put("out_bill_no", orderNo);
//        jsonObject.put("transfer_scene_id", "1000");
//        jsonObject.put("openid", openId);
//        jsonObject.put("transfer_amount", amount.multiply(new BigDecimal(100)).intValue());
//        jsonObject.put("transfer_remark", "地球岛提现");
//        jsonObject.put("notify_url", "https://api.ykjrzl.com/cb/wxpay/withdraw");
//        JSONArray transferSceneInfos = new JSONArray();
//        JSONObject scene = new JSONObject();
//        scene.put("info_type", "奖励说明");
//        scene.put("info_content", "地球岛提现");
//        transferSceneInfos.add(scene);
//        JSONObject scene1 = new JSONObject();
//        scene1.put("info_type", "活动名称");
//        scene1.put("info_content", "地球岛提现");
//        transferSceneInfos.add(scene1);
//        jsonObject.put("transfer_scene_report_infos", transferSceneInfos);
//        String authorization = mobileService.getAuthorizationSign("POST", "/v3/fund-app/mch-transfer/transfer-bills", jsonObject.toJSONString());
//        System.out.println(authorization);
//        Map<String, String> headers = new HashMap<>();
//        headers.put("Authorization", authorization);
//        headers.put("Content-Type", "application/json");
//        headers.put("Accept", "application/json");
//        headers.put("Wechatpay-Serial", "B342579AE01F70B3A2777860F69D608BE66866A");
//        System.out.println(jsonObject.toJSONString());
//        HttpResponse response = HttpRequest.post("https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills")
//                .addHeaders(headers).body(jsonObject.toJSONString()).execute();
//        JSONObject res = JSON.parseObject(response.body());
//        String channelOrderId = res.getString("transfer_bill_no");
//        String pkg = res.getString("package_info");
//        withdrawOrderService.generateOrder(userId, orderNo, channelOrderId, amount, PayType.WECHAT.getCode());
//        return pkg;
//    }
//
//    @Override
//    public UserWallet getByUserId(String userId) {
//        LambdaQueryWrapper<UserWallet> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(UserWallet::getUserId, userId);
//        return getOne(wrapper);
//    }
//
//    @Override
//    public Object chargeGold(BigDecimal amount, Integer payType) {
//        String orderNo = OrderIDGenerator.generateOrderId();
//        if (PayType.ALIPAY.getCode() == payType) {
//            walletOrderService.generateOrder(SecurityUtils.getUserId(), orderNo, amount, payType);
//            AlipayParams params = AlipayParams.builder()
//                    .orderNo(orderNo)
//                    .description("金币充值")
//                    .amount(amount)
//                    .callbackUrl("https://api.ykjrzl.com/cb/alipay/gold")
//                    .build();
//            return aliPayService.pay(params);
//        } else if (PayType.WECHAT.getCode() == payType) {
//            walletOrderService.generateOrder(SecurityUtils.getUserId(), orderNo, amount, payType);
//            Amount totalAmount = new Amount(amount.multiply(new BigDecimal(100)).intValue(), "cny");
//            PayParams params = PayParams.builder()
//                    .orderNo(orderNo)
//                    .description("金币充值")
//                    .amount(totalAmount)
//                    .callbackUrl("https://api.ykjrzl.com/cb/wxpay/gold")
//                    .build();
//            return getPaySign(mobileService.appPay(params).getPrepayId());
//        } else if (PayType.BALANCE.getCode() == payType) {
//            handleBalancePay(amount);
//        }
//        return null;
//    }
//
//    @Transactional
//    public void handleBalancePay(BigDecimal amount) {
//        UserWallet wallet = walletService.getByUserId(SecurityUtils.getUserId());
//        if (wallet.getBalance().compareTo(amount) < 0) {
//            throw new ServiceException("余额不足");
//        }
//        wallet.setBalance(wallet.getBalance().subtract(amount));
//        wallet.setGold(wallet.getGold() + amount.multiply(new BigDecimal(10)).intValue());
//        updateById(wallet);
//        walletRecordService.addRecord(SecurityUtils.getUserId(), amount, "out", "金币充值");
//        walletGoldRecordService.addRecord(SecurityUtils.getUserId(), amount.multiply(new BigDecimal(10)).intValue(), "in", "金币充值");
//    }
//
//    public PaySignParams getPaySign(String prepayId) {
//        PaySignParams signParams = PaySignParams.builder()
//                .nonceStr(UUID.randomUUID().toString().replace("-", ""))
//                .timestamp(System.currentTimeMillis() / 1000)
//                .prepayId(prepayId)
//                .mchId(mchId)
//                .build();
//        String sign = mobileService.getSign(signParams);
//        signParams.setSign(sign);
//        return signParams;
//    }
//}
