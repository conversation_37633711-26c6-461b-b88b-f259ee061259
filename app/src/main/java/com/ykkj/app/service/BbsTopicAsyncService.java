package com.ykkj.app.service;

import com.alibaba.fastjson.JSONObject;
import com.ykkj.app.service.inf.BbsTopicService;
import com.ykkj.core.utils.StringUtils;
import com.ykkj.core.utils.ip.Ip2RegionUtil;
import com.ykkj.mbp.entity.BbsTopic;
import com.ykkj.mbp.mapper.BbsTopicMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BbsTopicAsyncService {
    @Autowired
    private BbsTopicMapper topicMapper;
    @Autowired
    private EsService esService;

    @Async("taskExecutor")
    public void handleNewTopic(Long topicId) {
        BbsTopic topic = topicMapper.selectById(topicId);

        try {
            esService.addDocument("bbs_topic", topicId.toString(), topic);
        } catch (Exception e) {
            log.error("es添加topic失败-- {}", e.getMessage());
        }
        if(StringUtils.isEmpty(topic.getIp())){
            return;
        }
//        JSONObject ipAddress = Ip2RegionUtil.getInstance();
//        if(ipAddress != null) {
//            topic.setIpAddr(ipAddress.getString("regionName"));
//            topicMapper.updateById(topic);
//        }
    }
}
