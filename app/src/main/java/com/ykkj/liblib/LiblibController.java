package com.ykkj.liblib;

import cloud.liblibai.client.LibLib;
import cloud.liblibai.openapi.client.ApiException;
import cloud.liblibai.openapi.client.model.*;
import com.alibaba.fastjson.JSON;
import com.ykkj.core.domain.R;
import com.ykkj.liblib.pojo.*;
import com.ykkj.liblib.request.ImgRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/liblib")
public class LiblibController {
    @Autowired
    private LibLib libLib;

    @PostMapping("/text2img/v1")
    public R submitComfyTask(@RequestBody ImgRequest request) {
        List<Object> param = request.getParam();
        ComfyRequest comfyRequest = new ComfyRequest();
        comfyRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");

        ComfyRequestGenerateParams params = new ComfyRequestGenerateParams();
        params.setWorkflowUuid("85d592c569694f9f8e8c55c28514f9e4");
        KSamplerInput kSamplerInput = JSON.parseObject(JSON.toJSONString(param.get(0)), KSamplerInput.class);
        params.putAdditionalProperty("3",
                KSampler.builder()
                        .class_type("KSampler")
                        .inputs(
                                KSamplerInput.builder()
                                        .steps(kSamplerInput.getSteps())
                                        .cfg(kSamplerInput.getCfg())
                                        .sampler_name(kSamplerInput.getSampler_name())
                                        .scheduler(kSamplerInput.getScheduler())
                                        .denoise(kSamplerInput.getDenoise())
                                        .seed(kSamplerInput.getSeed())
                                        .build())
                        .build());
        CheckpointLoaderSimpleInput checkpointLoaderSimple = JSON.parseObject(JSON.toJSONString(param.get(1)), CheckpointLoaderSimpleInput.class);
        params.putAdditionalProperty("4",
                CheckpointLoaderSimple.builder()
                        .class_type("CheckpointLoaderSimple")
                        .inputs(
                                CheckpointLoaderSimpleInput.builder()
                                        .ckpt_name(checkpointLoaderSimple.getCkpt_name())
                                        .build())
                        .build());
        EmptyLatentImageInput emptyLatentImage = JSON.parseObject(JSON.toJSONString(param.get(2)), EmptyLatentImageInput.class);
        params.putAdditionalProperty("5",
                EmptyLatentImage.builder()
                        .class_type("EmptyLatentImage")
                        .inputs(
                                EmptyLatentImageInput.builder()
                                        .width(emptyLatentImage.getWidth())
                                        .height(emptyLatentImage.getHeight())
                                        .batch_size(emptyLatentImage.getBatch_size())
                                        .build())
                        .build());
        CLIPTextEncodeInput cLIPTextEncode = JSON.parseObject(JSON.toJSONString(param.get(3)), CLIPTextEncodeInput.class);
        params.putAdditionalProperty("6",
                CLIPTextEncode.builder().class_type("CLIPTextEncode")
                        .inputs(
                                CLIPTextEncodeInput.builder()
                                        .text(cLIPTextEncode.getText())
                                        .build())
                        .build());
        CLIPTextEncodeInput cLIPTextEncode1 = JSON.parseObject(JSON.toJSONString(param.get(4)), CLIPTextEncodeInput.class);
        params.putAdditionalProperty("7",
                CLIPTextEncode.builder().class_type("CLIPTextEncode")
                        .inputs(
                                CLIPTextEncodeInput.builder()
                                        .text(cLIPTextEncode1.getText())
                                        .build())
                        .build());
        comfyRequest.setGenerateParams(params);
        try {
            SubmitComfyResponse response = libLib.submitComfyTask(comfyRequest);
            System.out.println(response);
            System.out.println(response.getMsg());
            return R.ok(response.getData().getGenerateUuid());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/img2img/v1")
    public R img2imgV1(@RequestBody ImgRequest request){
        List<Object> param = request.getParam();
        ComfyRequest comfyRequest = new ComfyRequest();
        comfyRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        ComfyRequestGenerateParams params = new ComfyRequestGenerateParams();
        params.setWorkflowUuid("f71e687a4232472490f55e1d00de52a2");
        CLIPTextEncodeInput cLIPTextEncode = JSON.parseObject(JSON.toJSONString(param.get(0)), CLIPTextEncodeInput.class);
        params.putAdditionalProperty("63",
                CLIPTextEncode.builder().class_type("CLIPTextEncode")
                        .inputs(
                                CLIPTextEncodeInput.builder()
                                        .text(cLIPTextEncode.getText())
                                        .build()
                        )
                        .build());
        LoadImageInput loadImageInput = JSON.parseObject(JSON.toJSONString(param.get(1)), LoadImageInput.class);
        params.putAdditionalProperty("355",
                LoadImage.builder().class_type("LoadImage")
                        .inputs(
                                LoadImageInput.builder()
                                        .image(loadImageInput.getImage())
                                        .build()
                        )
                        .build());
        GroundingDinoSAMSegmentInput groundingDinoSAMSegmentInput = JSON.parseObject(JSON.toJSONString(param.get(2)), GroundingDinoSAMSegmentInput.class);
        params.putAdditionalProperty("511",
                GroundingDinoSAMSegment.builder().class_type("GroundingDinoSAMSegment (segment anything)")
                        .inputs(
                                GroundingDinoSAMSegmentInput.builder()
                                        .prompt(groundingDinoSAMSegmentInput.getPrompt())
                                        .build()
                        )
                        .build());
        CLIPTextEncodeInput cLIPTextEncode1 = JSON.parseObject(JSON.toJSONString(param.get(3)), CLIPTextEncodeInput.class);
        params.putAdditionalProperty("576",
                CLIPTextEncode.builder().class_type("CLIPTextEncode")
                        .inputs(
                                CLIPTextEncodeInput.builder()
                                        .text(cLIPTextEncode1.getText())
                                        .build())
                        .build());
        comfyRequest.setGenerateParams(params);
        try {
            SubmitComfyResponse response = libLib.submitComfyTask(comfyRequest);
            System.out.println(response);
            System.out.println(response.getMsg());
            return R.ok(response.getData().getGenerateUuid());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/img2img/v2")
    public R img2imgV2(@RequestBody ImgRequest request){
        List<Object> param = request.getParam();
        ComfyRequest comfyRequest = new ComfyRequest();
        comfyRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");
        ComfyRequestGenerateParams params = new ComfyRequestGenerateParams();
        params.setWorkflowUuid("caba020529da42139c926e3a56609172");
        LoadImageInput loadImageInput = JSON.parseObject(JSON.toJSONString(param.get(0)), LoadImageInput.class);
        params.putAdditionalProperty("5",
                LoadImage.builder().class_type("LoadImage")
                        .inputs(
                                LoadImageInput.builder()
                                        .image(loadImageInput.getImage())
                                        .build()
                        )
                        .build());
        LoadImageInput loadImageInput1 = JSON.parseObject(JSON.toJSONString(param.get(1)), LoadImageInput.class);
        params.putAdditionalProperty("7",
                LoadImage.builder().class_type("LoadImage")
                        .inputs(
                                LoadImageInput.builder()
                                        .image(loadImageInput1.getImage())
                                        .build()
                        )
                        .build());
        KSamplerInput kSamplerInput = JSON.parseObject(JSON.toJSONString(param.get(2)), KSamplerInput.class);
        params.putAdditionalProperty("28",
                KSampler.builder().class_type("KSampler")
                        .inputs(KSamplerInput.builder()
                                .cfg(kSamplerInput.getCfg())
                                .steps(kSamplerInput.getSteps())
                                .denoise(kSamplerInput.getDenoise())
                                .build())
                        .build());
        CLIPTextEncodeInput cLIPTextEncode = JSON.parseObject(JSON.toJSONString(param.get(3)), CLIPTextEncodeInput.class);
        params.putAdditionalProperty("31",
                CLIPTextEncode.builder().class_type("CLIPTextEncode")
                        .inputs(
                                CLIPTextEncodeInput.builder()
                                        .text(cLIPTextEncode.getText())
                                        .build())
                        .build());
        comfyRequest.setGenerateParams(params);
        try {
            SubmitComfyResponse response = libLib.submitComfyTask(comfyRequest);
            System.out.println(response);
            System.out.println(response.getMsg());
            return R.ok(response.getData().getGenerateUuid());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/result")
    public R getComfyTaskResult(String generateUuid) {
        ComfyStatusRequest request = new ComfyStatusRequest();
        request.setGenerateUuid(generateUuid);
        try {
            ComfyStatusResponse response = libLib.getComfyStatus(request);
            return R.ok(response.getData());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/img2img/v3")
    public R img2imgV3(@RequestBody ImgRequest request) {
        List<Object> param = request.getParam();
        ComfyRequest comfyRequest = new ComfyRequest();
        comfyRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");

        ComfyRequestGenerateParams params = new ComfyRequestGenerateParams();
        params.setWorkflowUuid("d214736fca6b4dad8ff29d35b9b658e");
        LoadImageInput loadImageInput = JSON.parseObject(JSON.toJSONString(param.get(0)), LoadImageInput.class);
        params.putAdditionalProperty("10",
                LoadImage.builder()
                        .class_type("LoadImage")
                        .inputs(
                                LoadImageInput.builder()
                                        .image(loadImageInput.getImage())
                                        .build())
                        .build());
        LayerUtilityColorPickerInput colorPickerInput = JSON.parseObject(JSON.toJSONString(param.get(1)), LayerUtilityColorPickerInput.class);
        params.putAdditionalProperty("11",
                LayerUtilityColorPicker.builder()
                        .class_type("LayerUtility: ColorPicker")
                        .inputs(
                                LayerUtilityColorPickerInput.builder()
                                        .color(colorPickerInput.getColor())
                                        .build())
                        .build());
        LayerUtilityColorPickerInput colorPickerInput1 = JSON.parseObject(JSON.toJSONString(param.get(2)), LayerUtilityColorPickerInput.class);
        params.putAdditionalProperty("12",
                LayerUtilityColorPicker.builder()
                        .class_type("LayerUtility: ColorPicker")
                        .inputs(
                                LayerUtilityColorPickerInput.builder()
                                        .color(colorPickerInput1.getColor())
                                        .build())
                        .build());
        LayerUtilityColorPickerInput colorPickerInput2 = JSON.parseObject(JSON.toJSONString(param.get(3)), LayerUtilityColorPickerInput.class);
        params.putAdditionalProperty("13",
                LayerUtilityColorPicker.builder()
                        .class_type("LayerUtility: ColorPicker")
                        .inputs(
                                LayerUtilityColorPickerInput.builder()
                                        .color(colorPickerInput2.getColor())
                                        .build())
                        .build());
        comfyRequest.setGenerateParams(params);
        try {
            SubmitComfyResponse response = libLib.submitComfyTask(comfyRequest);
            System.out.println(response);
            System.out.println(response.getMsg());
            return R.ok(response.getData().getGenerateUuid());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/text2img/v2")
    public R text2imgV2(@RequestBody ImgRequest request) {
        List<Object> param = request.getParam();
        ComfyRequest comfyRequest = new ComfyRequest();
        comfyRequest.setTemplateUuid("4df2efa0f18d46dc9758803e478eb51c");

        ComfyRequestGenerateParams params = new ComfyRequestGenerateParams();
        params.setWorkflowUuid("c71e629d5a044996b48b841cf649d528");
        EmptyLatentImageInput emptyLatentImage = JSON.parseObject(JSON.toJSONString(param.get(0)), EmptyLatentImageInput.class);
        params.putAdditionalProperty("5",
                EmptyLatentImage.builder()
                        .class_type("EmptyLatentImage")
                        .inputs(
                                EmptyLatentImageInput.builder()
                                        .batch_size(emptyLatentImage.getBatch_size())
                                        .build())
                        .build());
        BaiduTranslateNodeInput baiduTranslateNodeInput = JSON.parseObject(JSON.toJSONString(param.get(1)), BaiduTranslateNodeInput.class);
        params.putAdditionalProperty("53",
                BaiduTranslateNode.builder()
                        .class_type("BaiduTranslateNode")
                        .inputs(
                                BaiduTranslateNodeInput.builder()
                                        .text(baiduTranslateNodeInput.getText())
                                        .build())
                        .build());
        comfyRequest.setGenerateParams(params);
        try {
            SubmitComfyResponse response = libLib.submitComfyTask(comfyRequest);
            System.out.println(response);
            System.out.println(response.getMsg());
            return R.ok(response.getData().getGenerateUuid());
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }
}
