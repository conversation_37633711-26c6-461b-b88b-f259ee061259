package com.ykkj.liblib.pojo;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class KSampler {
    private String class_type = "KSampler";
    private KSamplerInput inputs;


//    @Data
//    @Builder
//    public static class Input {
//        private int steps;
//        private int cfg;
//        private String sampler_name;
//        private String scheduler;
//        private int denoise;
//        private long seed;
//
//        public Input() {
//
//        }
//    }
}
