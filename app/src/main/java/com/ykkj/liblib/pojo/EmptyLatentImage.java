package com.ykkj.liblib.pojo;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EmptyLatentImage {
    private String class_type = "EmptyLatentImage";
    private EmptyLatentImageInput inputs;

//    @Data
//    @Builder
//    public static class Input {
//        private int width = 512;
//        private int height = 512;
//        private int batch_size = 1;
//        public Input() {
//
//        }
//    }
}
