package com.ykkj.mbp.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

@Data
public class TopicDTO {
    private Long bbsId;
    private Long topicId;
    private String name;
    private String avatar;
    private String content;
    private String moduleName;

    private String title;
    private List<String> url;
}
