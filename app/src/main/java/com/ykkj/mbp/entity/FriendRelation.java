package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

@Data
@TableName("friend_relation")
public class FriendRelation {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String userId;
    private String friendUid;
    private String alias;
    private Integer blacked = 0;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;
}
