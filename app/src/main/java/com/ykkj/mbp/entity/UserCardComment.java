package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * BBS内容
 * <AUTHOR>
 * @date 2025/06/20 10:06
 */
@Data
@TableName("user_card_comment")
public class UserCardComment {

    @TableId(value = "id" , type = IdType.AUTO)
    private Long id;

    private String userId;

    private Long cardId;

    private String content;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    private Integer deleted;

}
