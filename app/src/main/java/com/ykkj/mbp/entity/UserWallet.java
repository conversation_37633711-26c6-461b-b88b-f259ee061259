package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
@TableName("user_wallet")
public class UserWallet{

    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private BigDecimal balance;

    private String alipayAccount;

    private String realname;

    private String password;

    private Integer gold = 0;

    private String openId;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

}
