package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/25 09:59
 */
@Data
@TableName("activity_info")
public class ActivityInfo {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String userId;

    private String cover;

    private String title;

    private String content;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    private Integer categoryId;

    private java.util.Date startTime;

    private java.util.Date endTime;

    private String address;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date createTime;

}
