package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/28 10:08
 */
@Data
@TableName("activity_order")
public class ActivityOrder {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String userId;

    private String orderNo;

    private String orderGoods;

    private Integer orderState;

    private BigDecimal orderAmount;

    private Integer payType;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;

}
