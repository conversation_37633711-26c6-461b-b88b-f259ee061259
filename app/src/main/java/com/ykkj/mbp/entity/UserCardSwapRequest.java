package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * null
 * <AUTHOR>
 * @date 2025/07/09 17:26
 */
@Data
@TableName("user_card_swap_request")
public class UserCardSwapRequest {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private Long cardId;

    private Integer status = 0;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;

}
