package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("user_password")
public class UserPassword {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String password;

    private String salt;

    private String resetCode;

    private Long resetCodeTime;

    private int tryCount;

    private Long lastTryTime;
}
