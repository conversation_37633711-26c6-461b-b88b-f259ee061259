package com.ykkj.mbp.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

@Data
@TableName("user_card")
public class UserCard {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String name;

    private String city;

    private String company;

    private Integer roleId;

    private String description;

    private String advantage;
    private String defect;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    private String phone;

    private String wechatId;

    private String income;

    private String introduction;

    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject platformIndex;

    @TableField(exist = false)
    private List<UserCardDisplayGroup> groups;

    @TableLogic(value = "0", delval = "1")
    private Integer deleted = 0;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;
}
