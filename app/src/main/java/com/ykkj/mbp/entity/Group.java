package com.ykkj.mbp.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("group_info")
public class Group {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String gid;

    private String name;

    private String avatar;

    private Integer categoryId;

    private String description;

    private Double longitude;

    private Double latitude;

    private String creator;

    private Integer audit = 0;

    private BigDecimal price = BigDecimal.ZERO;

    private Integer duration;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;

    @TableLogic(value = "0",delval = "1")
    private int deleted = 0;

}
