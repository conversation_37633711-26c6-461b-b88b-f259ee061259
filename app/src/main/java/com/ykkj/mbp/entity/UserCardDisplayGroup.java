package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * null
 * <AUTHOR>
 * @date 2025/06/06 10:37
 */
@Data
@TableName("user_card_display_group")
public class UserCardDisplayGroup {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String avatar;

    private String groupId;

    private String qrcode;

    private String groupName;

    private String description;

    private Long cardId;

    private String userId;

    private Integer groupType;

}
