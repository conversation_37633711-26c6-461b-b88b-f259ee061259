package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("group_card")
public class GroupCard {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String gid;

    private String userId;

    private String name;

    private String company;

    private String job;

    private String advantage;

    private String defect;

    private Integer hurryLevel;

    private Integer global = 0;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;
}
