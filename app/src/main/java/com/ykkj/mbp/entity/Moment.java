package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/14 14:09
 */
@Data
@TableName("moment")
public class Moment {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String address;

    private String content;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    private Double longitude;

    private Double latitude;

    private String userId;

    @TableLogic(value = "0",delval = "1")
    private Integer deleted = 0;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

}
