package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

@Data
@TableName("feedback")
public class Feedback {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String content;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    private String version;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

}
