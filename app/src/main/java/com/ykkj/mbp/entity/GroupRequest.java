package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * null
 * <AUTHOR>
 * @date 2025/05/26 14:31
 */
@Data
@TableName("group_request")
public class GroupRequest {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String userId;

    private String gid;

    private String reason;

    private Integer status = 0; //0 1通过 2拒绝

    private String remark;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date createTime;

    private String handleUserId;

}
