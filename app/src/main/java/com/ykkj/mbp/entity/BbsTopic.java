package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/14 14:09
 */
@Data
@TableName("bbs_topic")
public class BbsTopic {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String title;

    private String content;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    private Long bbsId;

    private Long moduleId;

    private String userId;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableLogic(value = "0",delval = "1")
    private Integer deleted = 0;

    private String ip;
    private String ipAddr;
}
