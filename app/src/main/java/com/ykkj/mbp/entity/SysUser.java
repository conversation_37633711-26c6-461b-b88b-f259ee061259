package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("sys_user")
public class SysUser implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String displayName;

    private String headImgUrl;

    private String unionId;

    private String openId;

    private String password;

    private String phone;

    private Integer delFlag = 0;

    private Integer status = 0;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;
}
