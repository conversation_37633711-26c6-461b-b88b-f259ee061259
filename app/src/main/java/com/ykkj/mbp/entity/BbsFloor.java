package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/14 16:09
 */
@Data
@TableName("bbs_floor")
public class BbsFloor {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long topicId;

    private String content;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    private String userId;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    private String ip;

    @TableLogic(value = "0",delval = "1")
    private Integer deleted = 0;
}
