package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * null
 * <AUTHOR>
 * @date 2025/06/11 15:36
 */
@Data
@TableName("feedback_reply")
public class FeedbackReply {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer feedbackId;

    private String content;

    @TableField(typeHandler = List2StringTypeHandler.class)
    private List<String> url;

    private String userId;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date createTime;

}
