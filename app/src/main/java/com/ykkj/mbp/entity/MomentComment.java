package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ykkj.mbp.typeHandler.List2StringTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/14 14:09
 */
@Data
@TableName("moment_comment")
public class MomentComment {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String content;

    private Long momentId;

    private String userId;

    private String toUserId;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableLogic(value = "0",delval = "1")
    private Integer deleted = 0;
}
