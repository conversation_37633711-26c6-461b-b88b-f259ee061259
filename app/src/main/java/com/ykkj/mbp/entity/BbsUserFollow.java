package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * null
 * <AUTHOR>
 * @date 2024/10/14 11:01
 */
@Data
@TableName("bbs_user_follow")
public class BbsUserFollow {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private Long bbsId;

    private Integer follow = 0;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    private Integer followCount = 0;

    private Integer unfollowCount = 0;

    private java.util.Date lastFollowTime;

    private java.util.Date lastUnfollowTime;

}
