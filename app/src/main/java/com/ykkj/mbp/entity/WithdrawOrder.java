package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("withdraw_order")
public class WithdrawOrder {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;

    private String orderNo;

    private BigDecimal amount;

    private Integer state = 0;

    private Integer payType;

    private String channelOrderId;

    private String remark;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;
}
