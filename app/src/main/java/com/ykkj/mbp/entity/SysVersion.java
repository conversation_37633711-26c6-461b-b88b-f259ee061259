package com.ykkj.mbp.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

@Data
@TableName("sys_version")
public class SysVersion {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String appType;

    private String appName;

    private String url;

    private String version;

    private String versionCode;

    private String content;

    private Integer hotfix;

    @TableField(fill = FieldFill.INSERT)
    private java.util.Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.util.Date updateTime;
}
