package com.ykkj.mbp.config;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MyPage<T> {
    private long pageNo = 1;
    private long pageSize = 10;
    private long total;
    private List<T> result;




    public MyPage getResult(IPage page){
        this.setPageNo((int) page.getCurrent());
        this.setPageSize((int) page.getSize());
        this.setResult(page.getRecords());
        this.setTotal((int) page.getTotal());
        return this;
    }

    public static MyPage getResult(IPage page,List record){
        MyPage retPage = new MyPage();
        retPage.setPageNo((int) page.getCurrent());
        retPage.setPageSize((int) page.getSize());
        retPage.setResult(record);
        retPage.setTotal((int) page.getTotal());
        return retPage;
    }

}
