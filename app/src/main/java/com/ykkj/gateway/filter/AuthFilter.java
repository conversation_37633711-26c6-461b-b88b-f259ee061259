package com.ykkj.gateway.filter;

import com.ykkj.core.constant.CacheConstants;
import com.ykkj.core.constant.HttpStatus;
import com.ykkj.core.constant.SecurityConstants;
import com.ykkj.core.constant.TokenConstants;
import com.ykkj.core.utils.ServletUtils;
import com.ykkj.core.utils.StringUtils;
import com.ykkj.core.utils.ip.IpUtils;
import com.ykkj.gateway.config.IgnoreWhiteProperties;
import com.ykkj.redis.service.RedisService;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Component
@Slf4j
public class AuthFilter implements  Filter, Ordered {
    @Autowired
    private IgnoreWhiteProperties ignoreWhite;
    @Autowired
    private RedisService redisService;

//    @Override
//    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
//        ServerHttpRequest request = exchange.getRequest();
//        ServerHttpRequest.Builder mutate = request.mutate();
//
//        String domain = "";
//        if(StringUtils.isEmpty(request.getHeaders().getOrigin())){
//            log.info("empty origin, ip = {}", request.getRemoteAddress().getHostString());
//        }else {
//            domain = request.getHeaders().getOrigin().replaceAll("http[s]?\\:\\/\\/", "");
//        }
//
//        String url = request.getURI().getPath();
//        String token = getToken(request);
//        // 跳过不需要验证的路径
//        if (StringUtils.matches(url, ignoreWhite.getWhites()) && StringUtils.isEmpty(token)) {
//            return chain.filter(exchange);
//        }
//        if (StringUtils.isEmpty(token)) {
//            return unauthorizedResponse(exchange, "token不能为空");
//        }
//        Claims claims = null;
//        try {
//            claims = JwtUtils.parseToken(token);
//        }catch (Exception e){
//            log.error("token解析失败！ token = {}", token);
//            return unauthorizedResponse(exchange, "token解析失败！");
//        }
//        if (claims == null) {
//            log.error("token已过期或验证不正确！token = {}", token);
//            return unauthorizedResponse(exchange, "token已过期或验证不正确！");
//        }
//        String userkey = JwtUtils.getUserKey(claims);
//        boolean islogin = redisService.hasKey(getTokenKey(userkey));
//        if (!islogin) {
//            return unauthorizedResponse(exchange, "登录状态已过期");
//        }
//        String userId = JwtUtils.getUserId(claims);
//        String username = JwtUtils.getUserName(claims);
//        if (StringUtils.isEmpty(userId)) {
//            return unauthorizedResponse(exchange, "令牌验证失败");
//        }
//        String userType = JwtUtils.getUserType(claims);
//        // 设置用户信息到请求
//        addHeader(mutate, SecurityConstants.USER_KEY, userkey);
//        addHeader(mutate, SecurityConstants.DETAILS_USER_ID, userId);
//        addHeader(mutate, SecurityConstants.DETAILS_USERNAME, username);
//        addHeader(mutate, SecurityConstants.DETAILS_USER_TYPE, userType);
//
//
//
//        // 内部请求来源参数清除
//        removeHeader(mutate, SecurityConstants.FROM_SOURCE);
//        return chain.filter(exchange.mutate().request(mutate.build()).build());
//    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String msg) {
        log.error("[鉴权异常处理]请求路径:{}, ip = {}", exchange.getRequest().getPath(), IpUtils.getIpAddr(exchange));
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.UNAUTHORIZED);
    }

    private void removeHeader(ServerHttpRequest.Builder mutate, String name) {
        mutate.headers(httpHeaders -> httpHeaders.remove(name)).build();
    }

    private void addHeader(HttpServletRequest mutate, String name, Object value) {
        if (value == null) {
            return;
        }
        String valueStr = value.toString();
        String valueEncode = ServletUtils.urlEncode(valueStr);
//        mutate.header(name, valueEncode);
    }

    private String getTokenKey(String token) {
        return CacheConstants.LOGIN_TOKEN_KEY + token;
    }

    /**
     * 获取请求token
     */
    private String getToken(HttpServletRequest request) {
        String token = request.getHeader(TokenConstants.AUTHENTICATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, StringUtil.EMPTY_STRING);
        }
        return token;
    }

    @Override
    public int getOrder() {
        return -200;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
//        ServerHttpRequest.Builder mutate = request.mutate();

        String domain = "";
        if(StringUtils.isEmpty(request.getHeader("origin"))){
            log.info("empty origin, ip = {}", request.getRemoteAddr());
        }else {
            domain = request.getHeader("origin").replaceAll("http[s]?\\:\\/\\/", "");
        }

        String url = request.getRequestURI();
        String token = getToken(request);
        // 跳过不需要验证的路径
        if (StringUtils.matches(url, ignoreWhite.getWhites()) && StringUtils.isEmpty(token)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
//            return chain.filter(exchange);
        }
        if (StringUtils.isEmpty(token)) {

            filterChain.doFilter(servletRequest, servletResponse);
            return;
//            return unauthorizedResponse(exchange, "token不能为空");
        }
//        Claims claims = null;
        try {
//            claims = JwtUtils.parseToken(token);

            filterChain.doFilter(servletRequest, servletResponse);
        }catch (Exception e){
            log.error("token解析失败！ token = {}", token);
            filterChain.doFilter(servletRequest, servletResponse);
            return;
//            return unauthorizedResponse(exchange, "token解析失败！");
        }
        if (token == null) {
            log.error("token已过期或验证不正确！token = {}", token);
            filterChain.doFilter(servletRequest, servletResponse);
            return;
//            return unauthorizedResponse(exchange, "token已过期或验证不正确！");
        }
        String userkey = "";
//        String userkey = JwtUtils.getUserKey(claims);
//        boolean islogin = redisService.hasKey(getTokenKey(userkey));
//        if (!islogin) {
//            return unauthorizedResponse(exchange, "登录状态已过期");
//        }
//        String userId = JwtUtils.getUserId(claims);
//        String username = JwtUtils.getUserName(claims);
        String userId = "";
        String username = "";


        if (StringUtils.isEmpty(userId)) {
//            return unauthorizedResponse(exchange, "令牌验证失败");
        }


//        String userType = JwtUtils.getUserType(claims);
        String userType = "";
        // 设置用户信息到请求
        addHeader(request, SecurityConstants.USER_KEY, userkey);
        addHeader(request, SecurityConstants.DETAILS_USER_ID, userId);
        addHeader(request, SecurityConstants.DETAILS_USERNAME, username);
        addHeader(request, SecurityConstants.DETAILS_USER_TYPE, userType);

        // 内部请求来源参数清除
//        removeHeader(request, SecurityConstants.FROM_SOURCE);

//        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
