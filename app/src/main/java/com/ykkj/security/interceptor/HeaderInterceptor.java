package com.ykkj.security.interceptor;


import com.ykkj.core.text.Convert;
import com.ykkj.security.auth.AuthUtil;
import com.ykkj.security.domain.LoginUser;
import com.ykkj.security.utils.SecurityUtils;
import com.ykkj.core.constant.SecurityConstants;
import com.ykkj.core.context.SecurityContextHolder;
import com.ykkj.core.utils.ServletUtils;
import com.ykkj.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 自定义请求头拦截器，将Header数据封装到线程变量中方便获取
 * 注意：此拦截器会同时验证当前用户有效期自动刷新有效期
 */
@Slf4j
public class HeaderInterceptor implements AsyncHandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        SecurityContextHolder.setUserId(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USER_ID));
        SecurityContextHolder.setUserName(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USERNAME));
        SecurityContextHolder.setUserKey(ServletUtils.getHeader(request, SecurityConstants.USER_KEY));
        SecurityContextHolder.setUserType(ServletUtils.getHeader(request, SecurityConstants.DETAILS_USER_TYPE));
        String tenantId = ServletUtils.getHeader(request, SecurityConstants.TENANT_ID);
        SecurityContextHolder.setTenantId(StringUtils.isEmpty(tenantId) ? "0" : tenantId);
        String token = SecurityUtils.getToken();
        if (StringUtils.isNotEmpty(token)) {
            LoginUser loginUser = AuthUtil.getLoginUser(token);
            if (StringUtils.isNotNull(loginUser)) {
                AuthUtil.verifyLoginUserExpire(loginUser);
                SecurityContextHolder.set(SecurityConstants.LOGIN_USER, loginUser);
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        SecurityContextHolder.remove();
    }
}
